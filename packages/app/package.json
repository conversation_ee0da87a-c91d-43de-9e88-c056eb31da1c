{"name": "@platform/app", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"postinstall": "setup-skia-web"}, "dependencies": {"@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/runtime": "^7.27.0", "@backpackapp-io/react-native-toast": "^0.14.0", "@dotlottie/react-player": "^1.6.19", "@expo/html-elements": "^0.11.0", "@expo/vector-icons": "^14.0.0", "@gluestack-ui/actionsheet": "^0.2.46", "@gluestack-ui/icon": "^0.1.25", "@gluestack-ui/input": "^0.1.32", "@gluestack-ui/nativewind-utils": "^1.0.23", "@gluestack-ui/radio": "^0.1.33", "@gluestack-ui/select": "^0.1.30", "@gorhom/portal": "^1.0.14", "@hookform/resolvers": "^3.9.0", "@legendapp/list": "^1.0.0-beta.8", "@legendapp/motion": "^2.4.0", "@lottiefiles/dotlottie-react": "^0.9.1", "@platform/assets": "workspace:*", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/native": "^7.0.0", "@rn-primitives/avatar": "^1.0.6", "@rn-primitives/checkbox": "^1.0.5", "@rn-primitives/dropdown-menu": "^1.1.0", "@rn-primitives/label": "^1.0.5", "@rn-primitives/popover": "^1.1.0", "@rn-primitives/portal": "^1.1.0", "@rn-primitives/progress": "^1.0.5", "@rn-primitives/radio-group": "^1.0.5", "@rn-primitives/select": "^1.0.6", "@rn-primitives/separator": "^1.0.5", "@rn-primitives/slot": "^1.1.0", "@rn-primitives/switch": "^1.0.5", "@rn-primitives/table": "^1.1.0", "@rn-primitives/toggle": "^1.1.0", "@rn-primitives/toggle-group": "^1.1.0", "@rn-primitives/tooltip": "^1.0.5", "@rn-primitives/types": "^1.1.0", "@shopify/flash-list": "^1.7.6", "@shopify/react-native-skia": "2.0.0-next.4", "@sideway/address": "^5.0.0", "@stripe/react-stripe-js": "^3.1.1", "@stripe/stripe-js": "^5.5.0", "@supabase/supabase-js": "^2.47.16", "@tanstack/react-query": "^5.59.20", "@tremor/react": "^3.18.6", "@usebasejump/shared": "^0.0.3", "aws4fetch": "^1.0.20", "axios": "^1.7.7", "brazilian-values": "^0.13.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cva": "^1.0.0-beta.2", "dayjs": "^1.11.13", "expo": "^53.0.20", "expo-constants": "~17.1.7", "expo-file-system": "~18.1.11", "expo-font": "~13.3.2", "expo-image": "~2.4.0", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-router": "~5.1.4", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.10", "input-otp-native": "^0.4.0", "lodash-es": "^4.17.21", "lottie-react-native": "^7.2.2", "mapbox-gl": "^3.7.0", "nativewind": "latest", "react": "19.0.0", "react-colorful": "^5.6.1", "react-dom": "19.0.0", "react-gifted-charts": "^0.0.6", "react-hook-form": "^7.55.0", "react-map-gl": "^7.1.7", "react-native": "0.79.5", "react-native-css-interop": "^0.1.22", "react-native-currency-input": "^1.1.1", "react-native-gesture-handler": "~2.24.0", "react-native-gifted-charts": "^1.4.49", "react-native-mask-input": "^1.2.3", "react-native-mmkv": "^3.2.0", "react-native-otp-entry": "^1.8.5", "react-native-reanimated": "3.17.5", "react-native-reanimated-carousel": "4.0.0-canary.17", "react-native-safe-area-context": "4.10.1", "react-native-screens": "^4.11.1", "react-native-svg": "15.11.2", "react-native-svg-transformer": "^1.5.0", "react-native-url-polyfill": "^2.0.0", "react-native-web": "~0.20.0", "tailwind-merge": "^2.5.4", "tailwind-variants": "^0.2.1", "tailwindcss-animate": "^1.0.7", "victory-native": "^41.14.0", "web": "^0.0.2", "zod": "^3.23.8"}, "devDependencies": {"@babel/core": "^7.20.0", "@biomejs/biome": "^1.9.4", "@hookform/devtools": "^4.3.3", "@types/lodash-es": "^4.17.12", "@types/react": "~19.0.14", "ajv": "^8.12.0", "dotenv-cli": "^8.0.0", "tailwindcss": "^3.4.0", "typescript": "~5.8.3", "wrangler": "^4.0.0"}, "expo": {"install": {"exclude": ["react-native-safe-area-context"]}}, "private": true}