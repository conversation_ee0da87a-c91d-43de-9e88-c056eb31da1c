import PlusIcon from "@/assets/icons/add.svg";
import TrashIcon from "@/assets/icons/delete-02.svg";
import WebsiteIcon from "@/assets/icons/globe-02.svg";
import ExternalLinkIcon from "@/assets/icons/link-square-01.svg";
import LiveIcon from "@/assets/icons/live-02.svg";
import PauseIcon from "@/assets/icons/pause.svg";
import RefreshIcon from "@/assets/icons/refresh.svg";
import SettingsIcon from "@/assets/icons/settings-01.svg";
import SaveIcon from "@/assets/icons/tick-02.svg";
import GeneralSettingsIcon from "@/assets/icons/settings-02.svg";
import ActiveGeneralSettingsIcon from "@/assets/icons/solid/settings-02.svg";
import AppearanceIcon from "@platform/assets/icons/paint-brush-04.svg";
import ActiveAppearanceIcon from "@platform/assets/icons/solid/paint-brush-04.svg";
import HomeIcon from "@/assets/icons/home-01.svg";
import ActiveHomeIcon from "@/assets/icons/solid/home-01.svg";
import { DomainVerificationBadge } from "@/components/DomainVerificationBadge";
import HorizontalTabs from "@/components/HorizontalTabs";
import Modal, { ModalFooter, ModalHeader } from "@/components/Modal";
import PageLayout, { PageRow } from "@/components/PageLayout";
import { SettingGroup } from "@/components/SettingGroup";
import { Badge, Box, Button, Center, ColorPicker, Heading, Input, Switch, Text, Textarea } from "@/components/ui";
import { useAxios, useCustomDomains, useSupabase } from "@/hooks";
import { useUploadWebsiteMedia } from "@/hooks/mutations/useUploadWebsiteMedia";
import { useWebsitesApi } from "@/hooks/useWebsitesApi";
import type { Website } from "@/types/websites";
import { cx, getCustomDomainUrl, getSubdomainUrl } from "@/utils";
import * as ImagePicker from "expo-image-picker";
// Domain validation is handled with regex
import { type ExternalPathString, Link, useLocalSearchParams, useRouter } from "expo-router";
import { useCallback, useEffect, useState } from "react";
import { Image } from "react-native";
import { ActivityIndicator, Alert, ScrollView } from "react-native";

export default function WebsiteDetails() {
  const { website_id } = useLocalSearchParams();
  const router = useRouter();
  const supabase = useSupabase();
  const { getWebsite, updateWebsite, deleteWebsite } = useWebsitesApi();
  const { mutateAsync: uploadWebsiteMedia } = useUploadWebsiteMedia();
  const { data: customDomainsData } = useCustomDomains({
    websiteId: website_id as string,
  });

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showDomainModal, setShowDomainModal] = useState(false);
  const [website, setWebsite] = useState<Website | null>(null);
  const [uploadingLogo, setUploadingLogo] = useState(false);
  const [uploadingHero, setUploadingHero] = useState(false);
  const [uploadingInstitutional, setUploadingInstitutional] = useState(false);
  const [activeTab, setActiveTab] = useState<"general" | "appearance" | "homepage">("general");

  const [formData, setFormData] = useState<{
    title: string;
    subdomain: string;
    description: string;
    published: boolean;
    theme?: {
      primary_color: string;
      secondary_color: string;
      font_family?: string;
    };
    logoImageToUpload?: {
      uri: string;
      fileName: string;
      type: "image";
    } | null;
    heroImageToUpload?: {
      uri: string;
      fileName: string;
      type: "image";
    } | null;
    institutionalImageToUpload?: {
      uri: string;
      fileName: string;
      type: "image";
    } | null;
  }>({
    title: "",
    subdomain: "",
    description: "",
    published: false,
    theme: {
      primary_color: "#3B82F6",
      secondary_color: "#10B981",
      font_family: "Inter",
    },
    logoImageToUpload: null,
    heroImageToUpload: null,
    institutionalImageToUpload: null,
  });

  const loadWebsite = useCallback(async () => {
    if (!website_id) return;

    setLoading(true);
    try {
      const data = await getWebsite(website_id as string);
      setWebsite(data);
      setFormData({
        title: data.title || "",
        subdomain: data.subdomain || "",
        description: data.description || "",
        published: data.published || false,
        theme: data.theme
          ? {
            primary_color: data.theme.primary_color,
            secondary_color: data.theme.secondary_color,
            font_family: data.theme.font_family,
          }
          : {
            primary_color: "#3B82F6",
            secondary_color: "#10B981",
            font_family: "Inter",
          },
      });

      // Custom domains are loaded separately via the useCustomDomains hook
    } catch (error) {
      console.error("Error loading website:", error);
      Alert.alert("Error", "Failed to load website details");
    } finally {
      setLoading(false);
    }
  }, [website_id]); // Removed getWebsite from dependencies

  useEffect(() => {
    loadWebsite();
    return undefined;
  }, [loadWebsite]);

  const handleUpdateWebsite = async () => {
    if (!website) return;

    setSaving(true);
    try {
      // Update the website data and theme in a single call
      const updatedWebsite = await updateWebsite(website.id, {
        title: formData.title,
        subdomain: formData.subdomain,
        description: formData.description,
        published: formData.published,
        theme: formData.theme ? {
          primary_color: formData.theme.primary_color,
          secondary_color: formData.theme.secondary_color,
          font_family: formData.theme.font_family,
        } : undefined,
      });

      // Update local state with the updated website
      setWebsite(updatedWebsite);

      // Upload logo image if selected
      if (formData.logoImageToUpload) {
        try {
          setUploadingLogo(true);

          // If there's an existing logo, delete it first
          if (website.logo_image_url) {
            // Extract the key from the URL
            const urlParts = website.logo_image_url.split("/");
            const keyParts = urlParts.slice(urlParts.indexOf("websites"));
            const key = keyParts.join("/");

            // Delete the old image
            try {
              await fetch(`${process.env.EXPO_PUBLIC_API_URL}/media/delete-file`, {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`,
                },
                body: JSON.stringify({ key }),
              });
              console.log(`Old logo image deleted: ${key}`);
            } catch (deleteError) {
              console.error("Error deleting old logo image:", deleteError);
              // Continue with upload even if delete fails
            }
          }

          // Upload the new logo
          const uploadResult = await uploadWebsiteMedia({
            websiteId: website.id,
            mediaFile: formData.logoImageToUpload,
            mediaType: "logo",
          });

          if (!uploadResult.success) {
            throw new Error(uploadResult.error || "Failed to upload logo image");
          }
        } catch (logoError) {
          console.error("Error uploading logo image:", logoError);
          Alert.alert(
            "Aviso",
            `O website foi atualizado, mas houve um erro ao fazer upload do logo: ${logoError instanceof Error ? logoError.message : String(logoError)}`,
          );
        } finally {
          setUploadingLogo(false);
        }
      }

      // Upload hero image if selected
      if (formData.heroImageToUpload) {
        try {
          setUploadingHero(true);

          // If there's an existing hero image, delete it first
          if (website.hero_image_url) {
            // Extract the key from the URL
            const urlParts = website.hero_image_url.split("/");
            const keyParts = urlParts.slice(urlParts.indexOf("websites"));
            const key = keyParts.join("/");

            // Delete the old image
            try {
              await fetch(`${process.env.EXPO_PUBLIC_API_URL}/media/delete-file`, {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`,
                },
                body: JSON.stringify({ key }),
              });
              console.log(`Old hero image deleted: ${key}`);
            } catch (deleteError) {
              console.error("Error deleting old hero image:", deleteError);
              // Continue with upload even if delete fails
            }
          }

          // Upload the new hero image
          const uploadResult = await uploadWebsiteMedia({
            websiteId: website.id,
            mediaFile: formData.heroImageToUpload,
            mediaType: "hero",
          });

          if (!uploadResult.success) {
            throw new Error(uploadResult.error || "Failed to upload hero image");
          }
        } catch (heroError) {
          console.error("Error uploading hero image:", heroError);
          Alert.alert(
            "Aviso",
            `O website foi atualizado, mas houve um erro ao fazer upload da imagem de capa: ${heroError instanceof Error ? heroError.message : String(heroError)}`,
          );
        } finally {
          setUploadingHero(false);
        }
      }

      // Upload institutional image if selected
      if (formData.institutionalImageToUpload) {
        try {
          setUploadingInstitutional(true);

          // If there's an existing institutional image, delete it first
          if (website.institutional_image_url) {
            // Extract the key from the URL
            const urlParts = website.institutional_image_url.split("/");
            const keyParts = urlParts.slice(urlParts.indexOf("websites"));
            const key = keyParts.join("/");

            // Delete the old image
            try {
              await fetch(`${process.env.EXPO_PUBLIC_API_URL}/media/delete-file`, {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`,
                },
                body: JSON.stringify({ key }),
              });
              console.log(`Old institutional image deleted: ${key}`);
            } catch (deleteError) {
              console.error("Error deleting old institutional image:", deleteError);
              // Continue with upload even if delete fails
            }
          }

          // Upload the new institutional image
          const uploadResult = await uploadWebsiteMedia({
            websiteId: website.id,
            mediaFile: formData.institutionalImageToUpload,
            mediaType: "institutional",
          });

          if (!uploadResult.success) {
            throw new Error(uploadResult.error || "Failed to upload institutional image");
          }
        } catch (institutionalError) {
          console.error("Error uploading institutional image:", institutionalError);
          Alert.alert(
            "Aviso",
            `O website foi atualizado, mas houve um erro ao fazer upload da imagem institucional: ${institutionalError instanceof Error ? institutionalError.message : String(institutionalError)}`,
          );
        } finally {
          setUploadingInstitutional(false);
        }
      }

      // Reload the website to get the updated data with theme and images
      await loadWebsite();

      // Reset image upload state
      setFormData((prev) => ({
        ...prev,
        logoImageToUpload: null,
        heroImageToUpload: null,
        institutionalImageToUpload: null,
      }));

      Alert.alert("Sucesso", "Website atualizado com sucesso");
    } catch (error) {
      console.error("Error updating website:", error);
      Alert.alert("Erro", "Falha ao atualizar o website");
    } finally {
      setSaving(false);
    }
  };

  const handleDeleteWebsite = () => {
    if (!website) return;
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    if (!website) return;

    setDeleting(true);
    try {
      // Delete the website (cascade deletion will handle related records)
      await deleteWebsite(website.id);
      setShowDeleteModal(false);
      Alert.alert("Sucesso", "Website deletado com sucesso");
      router.replace("/websites");
    } catch (error) {
      console.error("Error deleting website:", error);
      Alert.alert("Erro", "Falha ao deletar o website");
    } finally {
      setDeleting(false);
    }
  };

  const handleInputChange = (field: string, value: string | boolean): void => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handlePickImage = async (type: "logo" | "hero" | "institutional") => {
    try {
      if (!website) return;

      // Launch image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: "images",
        allowsEditing: true,
        quality: 0.8,
        aspect: type === "logo" ? [1, 1] : type === "hero" ? [16, 9] : [4, 3], // Square for logo, 16:9 for hero, 4:3 for institutional
      });

      if (result.canceled) {
        console.log("Image selection canceled");
        return;
      }

      const selectedAsset = result.assets[0];

      // Check file size (max 5MB)
      if (selectedAsset.fileSize && selectedAsset.fileSize > 5 * 1024 * 1024) {
        Alert.alert("Erro", "A imagem selecionada deve ter menos de 5MB");
        return;
      }

      // Store the selected image in formData to be uploaded when saving
      setFormData((prev) => ({
        ...prev,
        [type === "logo" ? "logoImageToUpload" : type === "hero" ? "heroImageToUpload" : "institutionalImageToUpload"]:
        {
          uri: selectedAsset.uri,
          fileName: selectedAsset.fileName || `${type}_image_${Date.now()}.jpg`,
          type: "image",
        },
      }));

      Alert.alert(
        "Imagem selecionada",
        `A ${type === "logo" ? "logo" : type === "hero" ? "imagem de capa" : "imagem institucional"} será atualizada quando você salvar as alterações.`,
      );
    } catch (error) {
      console.error(`Error selecting ${type} image:`, error);
      Alert.alert("Erro", `Falha ao selecionar a imagem: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  const handleThemeChange = (field: string, value: string): void => {
    setFormData((prev) => {
      const currentTheme = prev.theme || {
        primary_color: "#3B82F6",
        secondary_color: "#10B981",
        font_family: "Inter",
      };

      return {
        ...prev,
        theme: {
          ...currentTheme,
          [field]: value,
        },
      };
    });
  };

  if (loading) {
    return (
      <Box className="flex-1 items-center justify-center">
        <ActivityIndicator size="large" />
      </Box>
    );
  }

  return (
    <PageLayout
      pageTitle={website?.title || "Gerenciar website"}
      description="Gerencie e personalize seu site"
      pageIcon={WebsiteIcon}
      actions={[
        <Button
          size="sm"
          key="delete-website"
          variant="destructive-link"
          onPress={handleDeleteWebsite}
          className="mr-2"
        >
          <TrashIcon className="mr-2 w-[16px] stroke-2 text-text-inverse" />
          <Text>Deletar</Text>
        </Button>,
        <Button size="sm" key="save-website" onPress={handleUpdateWebsite} isLoading={saving}>
          <SaveIcon className="mr-2 w-[16px] text-text-inverse" />
          <Text>Salvar</Text>
        </Button>,
      ]}
    >
      {/* Delete Confirmation Modal */}
      <Modal isOpen={showDeleteModal} onClose={() => setShowDeleteModal(false)}>
        <Box className="p-6">
          <Heading size="lg" className="mb-4">
            Confirmar Exclusão
          </Heading>
          <Text className="mb-6">Tem certeza que deseja excluir o website "{website?.title}"?</Text>
          <Text className="text-sm text-text-secondary">
            O site será excluído imediatamente e de forma definitiva. Está ação NÃO pode ser desfeita.
          </Text>

          <Box className="mt-6 flex-row justify-end">
            <Button variant="outline" onPress={() => setShowDeleteModal(false)} className="mr-2">
              <Text>Cancelar</Text>
            </Button>
            <Button variant="destructive-link" onPress={confirmDelete} isLoading={deleting}>
              <Text>Confirmar e deletar</Text>
            </Button>
          </Box>
        </Box>
      </Modal>

      {/* Add Domain Modal */}
      <Modal isOpen={showDomainModal} onClose={() => setShowDomainModal(false)}>
        <Box className="p-6">
          <DomainForm website={website} onClose={() => setShowDomainModal(false)} />
        </Box>
      </Modal>

      <HorizontalTabs
        tabItems={[
          {
            id: "general",
            label: "Geral",
            onPress: () => setActiveTab("general"),
            isActive: activeTab === "general",
            icon: GeneralSettingsIcon,
            activeIcon: ActiveGeneralSettingsIcon,
          },
          {
            id: "appearance",
            label: "Aparência",
            onPress: () => setActiveTab("appearance"),
            isActive: activeTab === "appearance",
            icon: AppearanceIcon,
            activeIcon: ActiveAppearanceIcon,
          },
          {
            id: "homepage",
            label: "Página inicial",
            onPress: () => setActiveTab("homepage"),
            isActive: activeTab === "homepage",
            icon: HomeIcon,
            activeIcon: ActiveHomeIcon,
          },
        ]}
      />

      <ScrollView>
        {activeTab === "general" && (
          <>
            <SettingGroup
              title="Status de publicação"
              description="Controle se seu website está visível para o público ou em modo rascunho."
            >
              <Box className="gap-4">
                <Box className="flex-row items-center">
                  <Switch
                    checked={formData.published}
                    onCheckedChange={(checked) => handleInputChange("published", checked)}
                    size="md"
                    variant="status"
                  />
                  <Text
                    className={cx(
                      "ml-2 font-medium text-sm",
                      formData.published ? "text-success" : "text-destructive",
                    )}
                  >
                    {formData.published ? "Seu site está publicado" : "Seu site está ocultado!"}
                  </Text>
                </Box>
                <Text className="text-text-secondary text-sm">
                  {formData.published
                    ? "Seu website está online e acessível aos visitantes"
                    : "Seu website está modo rascunho e não pode ser acessado"}
                </Text>
                <Badge
                  className="w-fit"
                  variant={formData.published ? "primary-outline" : "outline"}
                  size="sm"
                  rounded="full"
                  beforeIcon={formData.published ? LiveIcon : PauseIcon}
                >
                  <Text>{formData.published ? "Publicado" : "Rascunho"}</Text>
                </Badge>
              </Box>
            </SettingGroup>

            <SettingGroup
              title="Subdomínio"
              description="Configure o endereço do seu website no subdomínio da Imoblr."
            >
              <Box className="gap-4">
                <Box>
                  <Text className="mb-1 font-medium">Subdomínio</Text>
                  <Input
                    placeholder="minha-imobiliaria"
                    value={formData.subdomain}
                    onChangeText={(value: string) => handleInputChange("subdomain", value)}
                  />
                  <Text className="mt-1 text-text-secondary text-sm">
                    Seu site ficará disponível em: {formData.subdomain || "[subdominio]"}.imoblr.com.br
                  </Text>
                </Box>
                <Link href={getSubdomainUrl(formData.subdomain) as ExternalPathString} asChild target="_blank">
                  <Button className="w-fit" variant="outline" size="sm">
                    <Text>{formData.subdomain}.imoblr.com.br</Text>
                    <ExternalLinkIcon className="ml-1 h-4 w-4" />
                  </Button>
                </Link>
              </Box>
            </SettingGroup>

            <SettingGroup
              title="Domínio personalizado"
              description="Configure um domínio próprio para seu website."
            >
              <Box className="gap-4">
                <Box className="flex-row items-center justify-between">
                  <Text className="font-medium">Domínios configurados</Text>
                  <Button
                    variant="outline"
                    size="sm"
                    onPress={() => setShowDomainModal(true)}
                  >
                    <PlusIcon className="mr-2 h-4 w-4" />
                    <Text>Adicionar domínio</Text>
                  </Button>
                </Box>
                {customDomainsData && customDomainsData.length > 0 ? (
                  <Box className="gap-3">
                    {customDomainsData.map((domain) => (
                      <Box key={domain.id} className="rounded-lg border border-border p-4">
                        <Box className="flex-row items-center justify-between">
                          <Link
                            href={getCustomDomainUrl(domain.domain_name) as ExternalPathString}
                            asChild
                            target="_blank"
                          >
                            <Button className="w-fit p-0" variant="link">
                              <Text className="font-medium">{domain.domain_name}</Text>
                              <ExternalLinkIcon className="ml-1 h-4 w-4" />
                            </Button>
                          </Link>
                          <Box className="flex-row items-center gap-2">
                            <Button size="xs" variant="destructive-outline">
                              <TrashIcon className="h-3 w-3" />
                            </Button>
                            <Button size="xs" variant="outline">
                              <SettingsIcon className="h-3 w-3" />
                            </Button>
                          </Box>
                        </Box>
                        <Box className="mt-2">
                          <DomainVerificationBadge domainId={domain.id} initialStatus={domain.verification_status} />
                        </Box>
                      </Box>
                    ))}
                  </Box>
                ) : (
                  <Box className="rounded-lg border border-border-light bg-background-darker p-6 text-center">
                    <Text className="text-text-secondary">Nenhum domínio personalizado configurado</Text>
                    <Text className="mt-1 text-text-tertiary text-sm">
                      Adicione um domínio próprio para personalizar o endereço do seu website
                    </Text>
                  </Box>
                )}
              </Box>
            </SettingGroup>
          </>
        )}

        {activeTab === "appearance" && (
          <>
            <SettingGroup
              title="Logo"
              description="Configure o logo que aparecerá no cabeçalho do seu website."
            >
              <Box className="gap-4">
                <Box className="flex-row items-center gap-4">
                  <Box className="h-24 w-24 rounded-lg border border-border overflow-hidden">
                    {formData.logoImageToUpload ? (
                      <Image
                        source={{ uri: formData.logoImageToUpload.uri }}
                        style={{ width: "100%", height: "100%" }}
                        resizeMode="cover"
                      />
                    ) : website && website.logo_image_url ? (
                      <Image
                        source={{ uri: website.logo_image_url }}
                        style={{ width: "100%", height: "100%" }}
                        resizeMode="cover"
                      />
                    ) : (
                      <Center className="h-full w-full bg-background-darker">
                        <Text className="text-text-tertiary text-xs text-center">Sem logo</Text>
                      </Center>
                    )}
                  </Box>
                  <Box className="flex-1">
                    <Button onPress={() => handlePickImage("logo")} isLoading={uploadingLogo} size="sm" variant="outline">
                      <Text>
                        {formData.logoImageToUpload
                          ? "Alterar seleção"
                          : website && website.logo_image_url
                            ? "Alterar logo"
                            : "Adicionar logo"}
                      </Text>
                    </Button>
                    <Text className="mt-2 text-xs text-text-tertiary">
                      Recomendado: Imagem quadrada de pelo menos 200x200px
                    </Text>
                  </Box>
                </Box>
              </Box>
            </SettingGroup>

            <SettingGroup
              title="Cores"
              description="Personalize as cores do seu website para combinar com sua marca."
            >
              <Box className="gap-4">
                <ColorPicker
                  label="Cor Primária"
                  placeholder="#3B82F6"
                  value={formData.theme?.primary_color || "#3B82F6"}
                  onChange={(color: string) => handleThemeChange("primary_color", color)}
                />
                <ColorPicker
                  label="Cor Secundária"
                  placeholder="#10B981"
                  value={formData.theme?.secondary_color || "#10B981"}
                  onChange={(color: string) => handleThemeChange("secondary_color", color)}
                />
              </Box>
            </SettingGroup>

            <SettingGroup
              title="Tipografia"
              description="Configure a fonte que será usada em todo o website."
            >
              <Box>
                <Text className="mb-1 font-medium">Família da fonte</Text>
                <Input
                  placeholder="Inter"
                  value={formData.theme?.font_family || ""}
                  onChangeText={(value: string) => handleThemeChange("font_family", value)}
                />
                <Text className="mt-1 text-text-secondary text-sm">
                  Exemplo: Inter, Roboto, Open Sans, Arial
                </Text>
              </Box>
            </SettingGroup>
          </>
        )}

        {activeTab === "homepage" && (
          <>
            <SettingGroup
              title="Título"
              description="Configure o título principal que aparecerá na página inicial do seu website."
            >
              <Box>
                <Text className="mb-1 font-medium">Título do website</Text>
                <Input
                  placeholder="Título do website"
                  value={formData.title}
                  onChangeText={(value: string) => handleInputChange("title", value)}
                />
                <Text className="mt-1 text-text-secondary text-sm">
                  Este será o título principal exibido na página inicial
                </Text>
              </Box>
            </SettingGroup>

            <SettingGroup
              title="Descrição"
              description="Adicione uma descrição que explique sobre sua imobiliária ou serviços."
            >
              <Box>
                <Text className="mb-1 font-medium">Descrição do website</Text>
                <Textarea
                  numberOfLines={4}
                  placeholder="Descrição do website"
                  value={formData.description}
                  onChangeText={(value: string) => handleInputChange("description", value)}
                />
                <Text className="mt-1 text-text-secondary text-sm">
                  Esta descrição aparecerá na página inicial abaixo do título
                </Text>
              </Box>
            </SettingGroup>

            <SettingGroup
              title="Imagem de capa"
              description="Configure a imagem principal que aparecerá no topo da página inicial."
            >
              <Box className="gap-4">
                <Box className="flex-row items-center gap-4">
                  <Box className="h-24 w-40 rounded-lg border border-border overflow-hidden">
                    {formData.heroImageToUpload ? (
                      <Image
                        source={{ uri: formData.heroImageToUpload.uri }}
                        style={{ width: "100%", height: "100%" }}
                        resizeMode="cover"
                      />
                    ) : website && website.hero_image_url ? (
                      <Image
                        source={{ uri: website.hero_image_url }}
                        style={{ width: "100%", height: "100%" }}
                        resizeMode="cover"
                      />
                    ) : (
                      <Center className="h-full w-full bg-background-darker">
                        <Text className="text-text-tertiary text-xs text-center">Sem imagem de capa</Text>
                      </Center>
                    )}
                  </Box>
                  <Box className="flex-1">
                    <Button onPress={() => handlePickImage("hero")} isLoading={uploadingHero} size="sm" variant="outline">
                      <Text>
                        {formData.heroImageToUpload
                          ? "Alterar seleção"
                          : website && website.hero_image_url
                            ? "Alterar imagem"
                            : "Adicionar imagem"}
                      </Text>
                    </Button>
                    <Text className="mt-2 text-xs text-text-tertiary">
                      Recomendado: Imagem no formato 16:9 de pelo menos 1200x675px
                    </Text>
                  </Box>
                </Box>
              </Box>
            </SettingGroup>

            <SettingGroup
              title="Imagem institucional"
              description="Configure uma imagem que representa sua empresa na seção 'Sobre nós'."
            >
              <Box className="gap-4">
                <Box className="flex-row items-center gap-4">
                  <Box className="h-24 w-32 rounded-lg border border-border overflow-hidden">
                    {formData.institutionalImageToUpload ? (
                      <Image
                        source={{ uri: formData.institutionalImageToUpload.uri }}
                        style={{ width: "100%", height: "100%" }}
                        resizeMode="cover"
                      />
                    ) : website && website.institutional_image_url ? (
                      <Image
                        source={{ uri: website.institutional_image_url }}
                        style={{ width: "100%", height: "100%" }}
                        resizeMode="cover"
                      />
                    ) : (
                      <Center className="h-full w-full bg-background-darker">
                        <Text className="text-text-tertiary text-xs text-center">Sem imagem institucional</Text>
                      </Center>
                    )}
                  </Box>
                  <Box className="flex-1">
                    <Button
                      onPress={() => handlePickImage("institutional")}
                      isLoading={uploadingInstitutional}
                      size="sm"
                      variant="outline"
                    >
                      <Text>
                        {formData.institutionalImageToUpload
                          ? "Alterar seleção"
                          : website && website.institutional_image_url
                            ? "Alterar imagem"
                            : "Adicionar imagem"}
                      </Text>
                    </Button>
                    <Text className="mt-2 text-xs text-text-tertiary">
                      Recomendado: Imagem no formato 4:3 de pelo menos 800x600px
                    </Text>
                  </Box>
                </Box>
              </Box>
            </SettingGroup>
          </>
        )}
      </ScrollView>
    </PageLayout>
  );
}

interface CloudflareVerificationData {
  id: string;
  hostname: string;
  ssl?: {
    status: string;
    method: string;
    type: string;
    validation_records?: Array<{
      txt_name?: string;
      txt_value?: string;
      http_url?: string;
      http_body?: string;
    }>;
  };
  status: string;
  verification_errors?: string[];
  ownership_verification?: {
    name: string;
    type: string;
    value: string;
  };
  ownership_verification_http?: {
    http_url: string;
    http_body: string;
  };
}

interface DomainResponseData {
  success: boolean;
  hostname: string;
  cloudflare_data: CloudflareVerificationData;
  error?: string;
  details?: unknown;
}

interface DomainFormProps {
  website: Website | null;
  onClose: () => void;
}

function DomainForm({ website, onClose }: DomainFormProps) {
  const [domainName, setDomainName] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isValid, setIsValid] = useState(false);
  const [step, setStep] = useState<"add" | "verify">("add");
  const [domainData, setDomainData] = useState<DomainResponseData | null>(null);
  const { axios } = useAxios();

  // Validate domain on change
  const handleDomainChange = (text: string) => {
    setDomainName(text);
    // Domain validation regex
    // This will accept domains like example.com, sub.example.com, and imoblrteste.com.br
    const domainRegex =
      /^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$/;
    const isValid = domainRegex.test(text);

    // Log validation result
    console.log("Domain validation:", { text, isValid });
    setIsValid(isValid);
    if (error && isValid) setError(null);
  };

  // Submit domain to API
  const handleSubmit = async () => {
    if (!isValid || !website) {
      console.log("Form validation failed:", { isValid, website });
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Make sure we have all required parameters
      if (!website.id) {
        throw new Error("ID do website não encontrado");
      }
      if (!website.team_account_id) {
        throw new Error("ID da conta não encontrado");
      }

      console.log("Submitting domain:", {
        hostname: domainName,
        websiteId: website.id,
        teamAccountId: website.team_account_id,
      });

      const response = await axios.post("/domains", {
        hostname: domainName,
        websiteId: website.id,
        teamAccountId: website.team_account_id,
      });

      console.log("Domain API response:", response.data);

      if (response.data.success) {
        // Store the domain data and move to verification step
        setDomainData(response.data);
        setStep("verify");
        Alert.alert("Sucesso", "Domínio adicionado com sucesso. Siga as instruções para verificação.");
      } else {
        setError(response.data.error || "Erro ao adicionar domínio");
        Alert.alert("Erro", response.data.error || "Erro ao adicionar domínio");
      }
    } catch (err) {
      console.error("Error adding domain:", err);
      const errorMessage =
        err instanceof Error
          ? err.message
          : "Erro ao adicionar domínio. Verifique se o domínio é válido e tente novamente.";

      setError(errorMessage);
      Alert.alert("Erro", errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle verification check
  const handleVerifyDomain = () => {
    // In a real implementation, this would check the verification status
    // For now, we'll just close the modal
    onClose();
  };

  // Handle "configure later" action
  const handleConfigureLater = () => {
    onClose();
  };

  // Generate verification instructions based on Cloudflare data
  const getVerificationInstructions = () => {
    if (!domainData?.cloudflare_data) return null;

    const result = domainData.cloudflare_data;

    // Check for TXT verification
    if (result.ownership_verification && result.ownership_verification.type === "txt") {
      return {
        type: "txt",
        name: result.ownership_verification.name,
        value: result.ownership_verification.value,
        instructions: `Adicione um registro TXT com nome ${result.ownership_verification.name} e valor ${result.ownership_verification.value} nas configurações de DNS.`,
      };
    }

    // Check for HTTP verification
    if (result.ownership_verification_http) {
      return {
        type: "http",
        url: result.ownership_verification_http.http_url,
        content: result.ownership_verification_http.http_body,
        instructions: `Crie um arquivo em ${result.ownership_verification_http.http_url} com o conteúdo: ${result.ownership_verification_http.http_body}`,
      };
    }

    // Check SSL validation records if available
    if (result.ssl?.validation_records && result.ssl.validation_records.length > 0) {
      const record = result.ssl.validation_records[0];

      if (record.txt_name && record.txt_value) {
        return {
          type: "txt",
          name: record.txt_name,
          value: record.txt_value,
          instructions: `Adicione um registro TXT com nome ${record.txt_name} e valor ${record.txt_value} nas configurações de DNS.`,
        };
      }

      if (record.http_url && record.http_body) {
        return {
          type: "http",
          url: record.http_url,
          content: record.http_body,
          instructions: `Crie um arquivo em ${record.http_url} com o conteúdo: ${record.http_body}`,
        };
      }
    }

    return null;
  };

  const verificationInfo = getVerificationInstructions();

  // Render the appropriate step
  if (step === "verify") {
    return (
      <Box className="space-y-4">
        <ModalHeader title="Configure seu domínio" onClose={onClose} icon={WebsiteIcon} />

        <Box className="gap-4">
          {verificationInfo ? (
            <>
              <Box className="rounded-md border border-border p-4">
                <Box className="mb-4 flex-row items-center gap-2">
                  <Center className="h-6 w-6 rounded-full bg-primary-100">
                    <Text className="text-primary-600">1</Text>
                  </Center>
                  <Text className="font-medium">Verifique a propriedade do domínio</Text>
                </Box>

                {verificationInfo.type === "txt" ? (
                  <Box className="space-y-3">
                    <Text className="text-sm">
                      Adicione um registro <Text className="font-medium">tipo TXT</Text> no DNS do seu domínio com as
                      seguintes informações:
                    </Text>

                    <Box className="space-y-2">
                      <Box className="flex-row justify-between">
                        <Text className="font-medium text-sm">Nome:</Text>
                        <Text className="text-sm">{verificationInfo.name}</Text>
                      </Box>

                      <Box className="flex-row justify-between">
                        <Text className="font-medium text-sm">Valor:</Text>
                        <Text className="text-sm">{verificationInfo.value}</Text>
                      </Box>
                    </Box>
                  </Box>
                ) : verificationInfo.type === "http" ? (
                  <Box className="space-y-3">
                    <Text className="text-sm">Crie um arquivo no seguinte caminho:</Text>

                    <Box className="space-y-2">
                      <Box className="flex-row justify-between">
                        <Text className="font-medium text-sm">URL:</Text>
                        <Text className="text-sm">{verificationInfo.url}</Text>
                      </Box>

                      <Box className="flex-row justify-between">
                        <Text className="font-medium text-sm">Conteúdo:</Text>
                        <Text className="text-sm">{verificationInfo.content}</Text>
                      </Box>
                    </Box>
                  </Box>
                ) : (
                  <Text className="text-sm">{verificationInfo.instructions}</Text>
                )}
              </Box>
              <Box className="rounded-md border border-border p-4">
                <Box className="mb-4 flex-row items-center gap-2">
                  <Center className="h-6 w-6 rounded-full bg-primary-100">
                    <Text className="text-primary-600">2</Text>
                  </Center>
                  <Text className="font-medium">Aponte o domínio para a Imoblr</Text>
                </Box>

                <Box className="space-y-3">
                  <Text className="text-sm">
                    Adicione os seguintes registros <Text className="font-medium">tipo CNAME</Text> no DNS do seu
                    domínio com as seguintes informações:
                  </Text>

                  <Box className="space-y-2">
                    <Box className="flex-row justify-between">
                      <Text className="font-medium text-sm">Nome:</Text>
                      <Text className="text-sm">@ (ou deixar em branco)</Text>
                      <Text className="font-medium text-sm">Valor:</Text>
                      <Text className="text-sm">sites.imoblr.com.br</Text>
                    </Box>
                  </Box>
                </Box>
              </Box>
            </>
          ) : (
            <Box className="rounded-md bg-destructive-50 p-3">
              <Text className="text-destructive-600 text-sm">
                Não foi possível obter instruções de verificação. Entre em contato com o suporte.
              </Text>
            </Box>
          )}

          {/* <Box className="rounded-md border border-primary-100 bg-primary-50 p-4 shadow-xs">
            <Text className="text-sm text-text-secondary">
              <Text className="font-medium text-sm">A verificação pode levar até 24 horas para ser concluída.</Text>Você
              pode verificar o status da verificação a qualquer momento na página de detalhes do website.
            </Text>
          </Box> */}
        </Box>

        <ModalFooter
          primaryAction={{
            iconBefore: RefreshIcon,
            label: "Checar status",
            onPress: handleVerifyDomain,
            variant: "outline",
          }}
          secondaryAction={{
            label: "Vou configurar depois",
            onPress: handleConfigureLater,
            variant: "destructive-link",
          }}
        />
      </Box>
    );
  }

  // Default step: Add domain
  return (
    <Box className="space-y-4">
      <ModalHeader title="Adicionar domínio" onClose={onClose} icon={WebsiteIcon} />
      <Box>
        <Text className="mb-1 font-medium">Domínio</Text>
        <Input
          placeholder="Exemplo: minha-imobiliaria.com.br"
          value={domainName}
          onChangeText={handleDomainChange}
          autoCapitalize="none"
          autoCorrect={false}
          autoFocus
          editable={!isSubmitting}
        />
        <Text className={`mt-2 text-xs ${isValid ? "text-success" : "text-text-secondary"}`}>
          {isValid
            ? "Domínio válido! Clique em 'Adicionar domínio' para continuar."
            : "Digite um domínio válido que você possui (ex: minha-imobiliaria.com.br)"}
        </Text>
      </Box>

      {error && (
        <Box className="rounded-md bg-destructive-50 p-3">
          <Text className="text-destructive-600 text-sm">{error}</Text>
        </Box>
      )}

      <Box className="mt-4 rounded-md border border-primary-100 bg-primary-50 p-4 shadow-xs">
        <Text className="text-sm text-text-secondary">
          Após adicionar seu domínio, você precisará configurar os registros DNS do seu provedor de domínio. Instruções
          detalhadas serão fornecidas após a adição do domínio.
        </Text>
      </Box>

      <ModalFooter
        primaryAction={{
          label: isSubmitting ? "Adicionando..." : "Adicionar domínio",
          onPress: handleSubmit,
          disabled: isSubmitting || !isValid,
        }}
        secondaryAction={{
          label: "Cancelar",
          onPress: onClose,
          disabled: isSubmitting,
        }}
      />
    </Box>
  );
}
